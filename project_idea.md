# 📝 TaskSphere - Multi-Company Task Management System

## 🔍 Project Overview
TaskSphere is a multi-tenant task management platform that allows multiple companies to manage and track internal tasks efficiently. Each company has a secure, isolated workspace where admins can manage groups, assign tasks, and monitor completion — while users maintain autonomy over their profiles. Advanced features like task swapping and group-based permissions make it suitable for scalable teams.

---

## 🧩 Core Entities

### Company
- Unique and isolated space for managing users, tasks, and groups.
- No data leakage across companies.

### Admin
- Responsible for managing groups and tasks.
- Can view user profiles and assign users to groups using an 8-digit unique User ID.
- Cannot create or edit user profiles directly.

### User
- Registers independently and is identified by a unique 8-digit alphanumeric User ID.
- Can manage their own tasks, participate in group activities, and initiate task swap requests.

### Groups
- Departments or teams within a company.
- Used to organize users and assign tasks efficiently.
- Users can be part of multiple groups.

### Tasks
- Created and managed by admins.
- Assigned to users or groups.
- Can be swapped with another user, subject to approval.

---

## 🛠️ Features to be Implemented

### 🔐 Authentication & Access Control
- User self-registration.
- Secure login with session-based or JWT tokens.
- Forgot/reset password flow.
- Role-based access (User/Admin).
- Company code required to join a specific organization.

---

### 👤 User Management (Updated)

#### User Registration:
- Users register themselves and receive a unique 8-digit alphanumeric User ID.

#### Admin Capabilities:
- Cannot create/edit/delete users.
- Can view user profile (read-only).
- Can add a user to a group by entering their 8-digit User ID.
- Can remove a user from a group.

---

### 👥 Group Management
Admin can:
- Create/edit/delete groups.
- Add/remove users by their User ID.
- View members of each group.
- Groups enable easier task assignment and organization.

---

### 🔁 Task Swap (Updated Flow)

- A user can initiate a task swap request with another user within the same group.

#### Swap Approval Workflow:
1. **Initiation**: User A initiates swap with User B.
2. **Admin Approval**: Request first goes to the Admin of the company.
3. **User B Approval**: After admin approves, User B receives the request and can accept or reject.
4. **Final Swap**: Only after both approvals, the task is reassigned.

- **Swap status**: Pending Admin, Pending User, Approved, Rejected.

---

### ✅ Task Management

#### Admin can:
- Create tasks with title, description, deadline, priority.
- Assign to individuals or groups.
- View task completion status.

#### User can:
- View their tasks.
- Update task progress/status.
- Request swap of assigned tasks.

---

### 📊 Task Status & Tracking

#### Statuses:
- Pending
- In Progress
- Blocked
- Completed

#### Admin dashboard:
- Task distribution by group/user.
- Completion and overdue tracking.

#### User dashboard:
- My Tasks view with filters and sort.

---

### 📅 Calendar & Deadlines
- View tasks in a calendar format.
- Task reminders and deadline highlights.

---

### 🔔 Notifications

#### For users:
- New task assigned.
- Swap request received.
- Deadline approaching.

#### For admins:
- Swap request pending approval.
- Overdue task alert.

---

### 📈 Reports & Analytics

#### Admin:
- Completion rate by group/user.
- Delayed tasks list.
- Task load distribution.
- Exportable: CSV, Excel, PDF.

---

### ⚙️ Custom Settings
- Define task categories.
- Customize labels and priorities.
- Configure company-wide notification settings.
